import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const bookingRef = searchParams.get('bookingRef');

    console.log(`=== BOOKING VERIFICATION API ===`);
    console.log(`Booking Reference: ${bookingRef}`);

    if (!bookingRef) {
      const errorResponse = NextResponse.json(
        { error: "Booking reference is required" },
        { status: 400 }
      );
      errorResponse.headers.set('Access-Control-Allow-Origin', '*');
      errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
      errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      return errorResponse;
    }

    // Call the external API to verify booking exists
    const apiUrl = 'https://ai.alviongs.com/webhook/v1/nibog/tickect/booking_ref/details';
    console.log(`Calling external API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        booking_ref_id: bookingRef
      }),
    });

    console.log(`External API response status: ${response.status}`);

    if (!response.ok) {
      console.error(`External API error: ${response.status}`);
      const errorResponse = NextResponse.json(
        { error: `Booking verification failed: ${response.status}` },
        { status: response.status }
      );
      errorResponse.headers.set('Access-Control-Allow-Origin', '*');
      errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
      errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      return errorResponse;
    }

    const bookingData = await response.json();
    console.log(`Booking verification result:`, bookingData);

    // Check if booking exists
    if (!bookingData || (Array.isArray(bookingData) && bookingData.length === 0)) {
      const notFoundResponse = NextResponse.json(
        { error: "Booking not found" },
        { status: 404 }
      );
      notFoundResponse.headers.set('Access-Control-Allow-Origin', '*');
      notFoundResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
      notFoundResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      return notFoundResponse;
    }

    // Return success with booking data
    const successResponse = NextResponse.json({
      success: true,
      booking: Array.isArray(bookingData) ? bookingData[0] : bookingData,
      message: "Booking verified successfully"
    }, { status: 200 });

    // Add CORS headers
    successResponse.headers.set('Access-Control-Allow-Origin', '*');
    successResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    successResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return successResponse;

  } catch (error: any) {
    console.error("Booking verification error:", error);
    const errorResponse = NextResponse.json(
      { error: error.message || "Failed to verify booking" },
      { status: 500 }
    );

    // Add CORS headers to error response
    errorResponse.headers.set('Access-Control-Allow-Origin', '*');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return errorResponse;
  }
}

// Handle preflight requests
export async function OPTIONS(request: Request) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
